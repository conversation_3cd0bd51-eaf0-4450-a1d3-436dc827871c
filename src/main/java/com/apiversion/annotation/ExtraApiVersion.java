package com.apiversion.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ExtraApiVersion注解，用于扩展API版本管理功能
 * 支持基于版本和区域的API路由
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExtraApiVersion {
    
    /**
     * API版本号
     * @return 版本号，默认为1
     */
    int value() default 1;
    
    /**
     * 区域匹配模式数组，支持正则表达式
     * @return 区域模式数组，默认为空数组（无区域限制）
     */
    String[] area() default {};
}