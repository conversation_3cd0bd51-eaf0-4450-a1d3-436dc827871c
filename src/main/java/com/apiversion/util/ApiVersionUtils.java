package com.apiversion.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * API版本工具类，提供通用的版本和区域处理功能
 */
public class ApiVersionUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ApiVersionUtils.class);
    
    /**
     * 验证正则表达式模式是否有效
     * 
     * @param pattern 正则表达式模式
     * @return 如果模式有效返回true，否则返回false
     */
    public static boolean isValidRegexPattern(String pattern) {
        if (pattern == null || pattern.trim().isEmpty()) {
            return false;
        }
        
        try {
            Pattern.compile(pattern);
            return true;
        } catch (PatternSyntaxException e) {
            logger.warn("无效的正则表达式模式: {} - {}", pattern, e.getMessage());
            return false;
        }
    }
    
    /**
     * 安全地编译正则表达式模式
     * 
     * @param pattern 正则表达式模式
     * @return 编译后的Pattern对象，如果编译失败返回null
     */
    public static Pattern safeCompilePattern(String pattern) {
        if (!isValidRegexPattern(pattern)) {
            return null;
        }
        
        try {
            return Pattern.compile(pattern);
        } catch (PatternSyntaxException e) {
            logger.error("正则表达式编译失败: {} - {}", pattern, e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证版本号格式
     * 
     * @param version 版本字符串
     * @return 如果格式有效返回true，否则返回false
     */
    public static boolean isValidVersionFormat(String version) {
        if (version == null || version.trim().isEmpty()) {
            return false;
        }
        
        return version.matches("^v\\d+$");
    }
    
    /**
     * 从版本字符串中提取版本号
     * 
     * @param version 版本字符串 (如 "v1", "v2")
     * @return 版本号，如果解析失败返回null
     */
    public static Integer extractVersionNumber(String version) {
        if (!isValidVersionFormat(version)) {
            logger.warn("无效的版本格式: {}", version);
            return null;
        }
        
        try {
            return Integer.valueOf(version.substring(1));
        } catch (NumberFormatException e) {
            logger.warn("版本号解析失败: {} - {}", version, e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证区域代码格式
     * 
     * @param area 区域代码
     * @return 如果格式看起来合理返回true，否则返回false
     */
    public static boolean isValidAreaFormat(String area) {
        if (area == null || area.trim().isEmpty()) {
            return false;
        }
        
        // 基本的区域代码格式验证 - 通常是数字
        return area.matches("^\\d+$") && area.length() >= 4 && area.length() <= 8;
    }
    
    /**
     * 记录条件匹配的详细信息
     * 
     * @param logger 日志记录器
     * @param requestVersion 请求版本
     * @param requestArea 请求区域
     * @param annotationVersion 注解版本
     * @param annotationAreas 注解区域模式
     * @param matchResult 匹配结果
     */
    public static void logMatchingDetails(Logger logger, String requestVersion, String requestArea, 
                                        int annotationVersion, String[] annotationAreas, boolean matchResult) {
        if (logger.isDebugEnabled()) {
            logger.debug("条件匹配详情: 请求[版本={}, 区域={}] vs 注解[版本={}, 区域模式={}] => 结果: {}", 
                        requestVersion, requestArea, annotationVersion, 
                        annotationAreas != null ? String.join(",", annotationAreas) : "无", 
                        matchResult ? "匹配" : "不匹配");
        }
    }
}