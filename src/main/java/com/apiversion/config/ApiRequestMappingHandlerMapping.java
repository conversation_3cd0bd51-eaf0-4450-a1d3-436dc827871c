package com.apiversion.config;

import com.apiversion.annotation.ExtraApiVersion;
import com.shensu.common.base.annotation.ApiVersion;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.servlet.mvc.condition.RequestCondition;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;

public class ApiRequestMappingHandlerMapping extends RequestMappingHandlerMapping {
    
    // 对类修饰的注解
    @Override
    protected RequestCondition<?> getCustomTypeCondition(Class<?> handlerType) {
        // 优先检查ExtraApiVersion注解
        ExtraApiVersion extraApiVersion = AnnotationUtils.findAnnotation(handlerType, ExtraApiVersion.class);
        if (extraApiVersion != null) {
            return createExtraCondition(extraApiVersion);
        }
        
        // 检查原有的ApiVersion注解
        ApiVersion apiVersion = AnnotationUtils.findAnnotation(handlerType, ApiVersion.class);
        return createCondition(apiVersion);
    }

    // 对方法修饰的注解
    @Override
    protected RequestCondition<?> getCustomMethodCondition(Method method) {
        // 优先检查ExtraApiVersion注解
        ExtraApiVersion extraApiVersion = AnnotationUtils.findAnnotation(method, ExtraApiVersion.class);
        if (extraApiVersion != null) {
            return createExtraCondition(extraApiVersion);
        }
        
        // 检查原有的ApiVersion注解
        ApiVersion apiVersion = AnnotationUtils.findAnnotation(method, ApiVersion.class);
        return createCondition(apiVersion);
    }

    // 创建基于@ApiVersion的RequestCondition（保持原有逻辑不变）
    private RequestCondition<ApiVersionCondition> createCondition(ApiVersion apiVersion) {
        // 原有的ApiVersion注解可能没有area字段，使用空数组作为默认值
        return apiVersion == null ? null : new ApiVersionCondition(apiVersion.value(), new String[]{});
    }
    
    // 创建基于@ExtraApiVersion的RequestCondition
    private RequestCondition<ExtraApiVersionCondition> createExtraCondition(ExtraApiVersion extraApiVersion) {
        return extraApiVersion == null ? null : new ExtraApiVersionCondition(extraApiVersion.value(), extraApiVersion.area());
    }
}