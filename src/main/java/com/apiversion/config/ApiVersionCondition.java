package com.apiversion.config;

import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.condition.RequestCondition;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ApiVersionCondition implements RequestCondition<ApiVersionCondition> {
    private final static Pattern VERSION_PREFIX_PATTERN = Pattern.compile("v(\\d+)");

    // header中的key
    private final static String HEADER_VERSION = "version";
    private final static String HEADER_AREA = "area";

    private int apiVersion;
    private String[] area;

    public ApiVersionCondition(int apiVersion, String[] area) {
        this.apiVersion = apiVersion;
        this.area = area;
    }

    public int getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(int apiVersion) {
        this.apiVersion = apiVersion;
    }

    public String[] getArea() {
        return area;
    }

    public void setArea(String[] area) {
        this.area = area;
    }

    @Override
    public ApiVersionCondition combine(ApiVersionCondition apiVersionCondition) {
        return new ApiVersionCondition(apiVersionCondition.getApiVersion(), apiVersionCondition.getArea());
    }

    @Override
    public ApiVersionCondition getMatchingCondition(HttpServletRequest httpServletRequest) {
        String apiVersion = httpServletRequest.getHeader(HEADER_VERSION);
        String area = httpServletRequest.getHeader(HEADER_AREA);
        if (!StringUtils.isEmpty(apiVersion)) {
            Matcher m = VERSION_PREFIX_PATTERN.matcher(apiVersion);
            if (m.find()) {
                Integer version = Integer.valueOf(m.group(1));
                if (version >= this.apiVersion) {
                    // 如果有传入平台platform参数，那么就找指定了平台的接口，找不到该接口就不通过
                    if(!StringUtils.isEmpty(area)) {
                        if(this.area.length > 0) {
                            // 接口做了地区限制,必须要校验
                            for (String str : this.area) {
                                Pattern pattern = Pattern.compile(str);
                                Matcher matcher = pattern.matcher(area);
                                if(matcher.find()) {
                                    return this;
                                }
                            }
                            return null;
                        } else {
                            // 接口没有做地区校验,不需要校验
                            return this;
                        }
                    }
                    if(this.area.length > 0) {
                        // 接口做了地区限制,必须要校验
                        return null;
                    }
                    return this;
                }
            }
            return null;
        } else {
            return this;
        }
    }

    @Override
    public int compareTo(ApiVersionCondition apiVersionCondition, HttpServletRequest httpServletRequest) {
        return apiVersionCondition.getApiVersion() - this.apiVersion;
    }
}
