package com.apiversion.config;

import com.apiversion.util.ApiVersionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.condition.RequestCondition;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * ExtraApiVersion条件类，实现基于版本和区域的请求匹配逻辑
 */
public class ExtraApiVersionCondition implements RequestCondition<ExtraApiVersionCondition> {
    
    private static final Logger logger = LoggerFactory.getLogger(ExtraApiVersionCondition.class);
    
    private static final Pattern VERSION_PREFIX_PATTERN = Pattern.compile("v(\\d+)");
    
    // 请求头中的key
    private static final String HEADER_VERSION = "version";
    private static final String HEADER_AREA = "area";
    
    private final int apiVersion;
    private final String[] area;
    
    public ExtraApiVersionCondition(int apiVersion, String[] area) {
        this.apiVersion = apiVersion;
        this.area = area != null ? area : new String[0];
    }
    
    public int getApiVersion() {
        return apiVersion;
    }
    
    public String[] getArea() {
        return area;
    }
    
    @Override
    public ExtraApiVersionCondition combine(ExtraApiVersionCondition other) {
        // 方法级别的注解优先于类级别的注解
        return new ExtraApiVersionCondition(other.getApiVersion(), other.getArea());
    }
    
    @Override
    public ExtraApiVersionCondition getMatchingCondition(HttpServletRequest request) {
        String versionHeader = request.getHeader(HEADER_VERSION);
        String areaHeader = request.getHeader(HEADER_AREA);
        
        logger.debug("开始匹配条件: 请求版本={}, 请求区域={}, 注解版本={}, 注解区域模式数量={}", 
                    versionHeader, areaHeader, this.apiVersion, this.area.length);
        
        // 版本匹配逻辑
        if (!StringUtils.isEmpty(versionHeader)) {
            Integer requestVersion = ApiVersionUtils.extractVersionNumber(versionHeader);
            if (requestVersion != null) {
                if (requestVersion < this.apiVersion) {
                    logger.debug("版本不匹配: 请求版本 {} < 注解版本 {}", requestVersion, this.apiVersion);
                    ApiVersionUtils.logMatchingDetails(logger, versionHeader, areaHeader, this.apiVersion, this.area, false);
                    return null;
                }
                logger.debug("版本匹配成功: 请求版本 {} >= 注解版本 {}", requestVersion, this.apiVersion);
            } else {
                logger.warn("无效的版本格式: {}, 期望格式为 vN (如 v1, v2)", versionHeader);
                // 版本格式无效时，继续处理，相当于没有版本限制
            }
        } else {
            logger.debug("请求中没有版本头，匹配所有版本");
        }
        
        // 区域匹配逻辑
        if (!StringUtils.isEmpty(areaHeader)) {
            if (this.area.length > 0) {
                // 接口指定了区域限制，必须匹配
                boolean areaMatched = false;
                int validPatternCount = 0;
                
                for (String areaPattern : this.area) {
                    if (StringUtils.isEmpty(areaPattern)) {
                        logger.warn("发现空的区域模式，跳过");
                        continue;
                    }
                    
                    Pattern pattern = ApiVersionUtils.safeCompilePattern(areaPattern);
                    if (pattern != null) {
                        validPatternCount++;
                        Matcher areaMatcher = pattern.matcher(areaHeader);
                        if (areaMatcher.find()) {
                            areaMatched = true;
                            logger.debug("区域匹配成功: {} 匹配模式 {}", areaHeader, areaPattern);
                            break;
                        } else {
                            logger.debug("区域不匹配模式: {} 不匹配 {}", areaHeader, areaPattern);
                        }
                    }
                }
                
                if (validPatternCount == 0) {
                    logger.warn("所有区域模式都无效，条件匹配失败");
                    return null;
                }
                
                if (!areaMatched) {
                    logger.debug("区域不匹配: {} 不匹配任何有效模式", areaHeader);
                    return null;
                }
            } else {
                logger.debug("接口没有区域限制，区域匹配通过");
            }
        } else {
            // 请求中没有area头
            if (this.area.length > 0) {
                // 接口指定了区域限制，但请求没有提供区域信息
                logger.debug("请求缺少area头，但接口指定了区域限制，匹配失败");
                return null;
            } else {
                logger.debug("请求没有area头，接口也没有区域限制，匹配通过");
            }
        }
        
        logger.debug("条件匹配成功: 版本={}, 区域模式数量={}", this.apiVersion, this.area.length);
        ApiVersionUtils.logMatchingDetails(logger, versionHeader, areaHeader, this.apiVersion, this.area, true);
        return this;
    }
    
    @Override
    public int compareTo(ExtraApiVersionCondition other, HttpServletRequest request) {
        // 版本号高的优先
        int versionCompare = other.getApiVersion() - this.apiVersion;
        if (versionCompare != 0) {
            return versionCompare;
        }
        
        // 版本相同时，有区域限制的优先
        int thisAreaCount = this.area.length;
        int otherAreaCount = other.getArea().length;
        return otherAreaCount - thisAreaCount;
    }
}