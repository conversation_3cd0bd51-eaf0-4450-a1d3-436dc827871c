package com.apiversion.controller;

import com.shensu.common.base.annotation.ApiVersion;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class UserController {
    
    // 原有的ApiVersion注解示例（保持不变）
    @ApiVersion(value = 1)
    @GetMapping("/login")
    public String login() {
        return "this api version is v1";
    }

    @ApiVersion(value = 2)
    @GetMapping("/login")
    public String login2() {
        return "this api version is v2";
    }

    @ApiVersion(value = 3)
    @GetMapping("/login")
    public String login3() {
        return "this api version is v3";
    }

    @ApiVersion(value = 4)
    @GetMapping("/login")
    public String login4() {
        return "this api version is v4";
    }

    @ApiVersion(value = 5)
    @GetMapping("/login")
    public String login5() {
        return "this api version is v5";
    }
    
}
