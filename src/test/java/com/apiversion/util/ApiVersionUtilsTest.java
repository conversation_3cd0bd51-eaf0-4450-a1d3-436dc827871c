package com.apiversion.util;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

class ApiVersionUtilsTest {

    private static final Logger logger = LoggerFactory.getLogger(ApiVersionUtilsTest.class);

    @Test
    void testIsValidRegexPattern() {
        // 有效的正则表达式
        assertTrue(ApiVersionUtils.isValidRegexPattern("^\\d+$"));
        assertTrue(ApiVersionUtils.isValidRegexPattern("^14\\d{4}$"));
        assertTrue(ApiVersionUtils.isValidRegexPattern(".*"));
        
        // 无效的正则表达式
        assertFalse(ApiVersionUtils.isValidRegexPattern("[invalid"));
        assertFalse(ApiVersionUtils.isValidRegexPattern("*invalid"));
        
        // 边界情况
        assertFalse(ApiVersionUtils.isValidRegexPattern(null));
        assertFalse(ApiVersionUtils.isValidRegexPattern(""));
        assertFalse(ApiVersionUtils.isValidRegexPattern("   "));
    }

    @Test
    void testSafeCompilePattern() {
        // 有效的模式
        Pattern pattern = ApiVersionUtils.safeCompilePattern("^\\d+$");
        assertNotNull(pattern);
        assertTrue(pattern.matcher("123").matches());
        
        // 无效的模式
        Pattern invalidPattern = ApiVersionUtils.safeCompilePattern("[invalid");
        assertNull(invalidPattern);
        
        // 边界情况
        assertNull(ApiVersionUtils.safeCompilePattern(null));
        assertNull(ApiVersionUtils.safeCompilePattern(""));
    }

    @Test
    void testIsValidVersionFormat() {
        // 有效的版本格式
        assertTrue(ApiVersionUtils.isValidVersionFormat("v1"));
        assertTrue(ApiVersionUtils.isValidVersionFormat("v10"));
        assertTrue(ApiVersionUtils.isValidVersionFormat("v999"));
        
        // 无效的版本格式
        assertFalse(ApiVersionUtils.isValidVersionFormat("1"));
        assertFalse(ApiVersionUtils.isValidVersionFormat("version1"));
        assertFalse(ApiVersionUtils.isValidVersionFormat("v"));
        assertFalse(ApiVersionUtils.isValidVersionFormat("va"));
        assertFalse(ApiVersionUtils.isValidVersionFormat("v1.0"));
        
        // 边界情况
        assertFalse(ApiVersionUtils.isValidVersionFormat(null));
        assertFalse(ApiVersionUtils.isValidVersionFormat(""));
        assertFalse(ApiVersionUtils.isValidVersionFormat("   "));
    }

    @Test
    void testExtractVersionNumber() {
        // 有效的版本号提取
        assertEquals(Integer.valueOf(1), ApiVersionUtils.extractVersionNumber("v1"));
        assertEquals(Integer.valueOf(10), ApiVersionUtils.extractVersionNumber("v10"));
        assertEquals(Integer.valueOf(999), ApiVersionUtils.extractVersionNumber("v999"));
        
        // 无效的版本格式
        assertNull(ApiVersionUtils.extractVersionNumber("1"));
        assertNull(ApiVersionUtils.extractVersionNumber("version1"));
        assertNull(ApiVersionUtils.extractVersionNumber("v"));
        assertNull(ApiVersionUtils.extractVersionNumber("va"));
        
        // 边界情况
        assertNull(ApiVersionUtils.extractVersionNumber(null));
        assertNull(ApiVersionUtils.extractVersionNumber(""));
    }

    @Test
    void testIsValidAreaFormat() {
        // 有效的区域格式
        assertTrue(ApiVersionUtils.isValidAreaFormat("1234"));
        assertTrue(ApiVersionUtils.isValidAreaFormat("140000"));
        assertTrue(ApiVersionUtils.isValidAreaFormat("12345678"));
        
        // 无效的区域格式
        assertFalse(ApiVersionUtils.isValidAreaFormat("123")); // 太短
        assertFalse(ApiVersionUtils.isValidAreaFormat("123456789")); // 太长
        assertFalse(ApiVersionUtils.isValidAreaFormat("12a4")); // 包含字母
        assertFalse(ApiVersionUtils.isValidAreaFormat("12-34")); // 包含特殊字符
        
        // 边界情况
        assertFalse(ApiVersionUtils.isValidAreaFormat(null));
        assertFalse(ApiVersionUtils.isValidAreaFormat(""));
        assertFalse(ApiVersionUtils.isValidAreaFormat("   "));
    }

    @Test
    void testLogMatchingDetails() {
        // 测试日志记录方法不会抛出异常
        String[] areas = {"^14\\d{4}$", "^32\\d{4}$"};
        
        assertDoesNotThrow(() -> {
            ApiVersionUtils.logMatchingDetails(logger, "v1", "140000", 1, areas, true);
            ApiVersionUtils.logMatchingDetails(logger, "v2", "999999", 1, areas, false);
            ApiVersionUtils.logMatchingDetails(logger, null, null, 1, null, false);
            ApiVersionUtils.logMatchingDetails(logger, "", "", 1, new String[]{}, true);
        });
    }
}