package com.apiversion.controller;

import com.apiversion.annotation.ExtraApiVersion;
import com.shensu.common.base.annotation.ApiVersion;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器，用于验证ExtraApiVersion功能
 */
@RestController
public class TestController {
    
    // 原有的ApiVersion注解测试
    @ApiVersion(value = 1)
    @GetMapping("/test-api")
    public String testApi1() {
        return "ApiVersion v1";
    }
    
    @ApiVersion(value = 2)
    @GetMapping("/test-api")
    public String testApi2() {
        return "ApiVersion v2";
    }
    
    // ExtraApiVersion注解测试
    @ExtraApiVersion(value = 1, area = {"^14\\d{4}$"})
    @GetMapping("/test-extra")
    public String testExtra1() {
        return "ExtraApiVersion v1 for area 14xxxx";
    }
    
    @ExtraApiVersion(value = 2, area = {"^14\\d{4}$", "^32\\d{4}$"})
    @GetMapping("/test-extra")
    public String testExtra2() {
        return "ExtraApiVersion v2 for areas 14xxxx and 32xxxx";
    }
    
    @ExtraApiVersion(value = 3, area = {"^33\\d{4}$"})
    @GetMapping("/test-extra")
    public String testExtra3() {
        return "ExtraApiVersion v3 for area 33xxxx";
    }
    
    @ExtraApiVersion(value = 4)
    @GetMapping("/test-extra")
    public String testExtra4() {
        return "ExtraApiVersion v4 without area restriction";
    }
    
    // 混合测试
    @ExtraApiVersion(value = 1, area = {"^\\d{6}$"})
    @GetMapping("/test-mixed")
    public String testMixed() {
        return "Mixed test with 6-digit area";
    }
    
    @ApiVersion(value = 1)
    @GetMapping("/test-original")
    public String testOriginal() {
        return "Original ApiVersion test";
    }
}