package com.apiversion.integration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureWebMvc
@TestPropertySource(properties = {
    "spring.main.allow-bean-definition-overriding=true"
})
class ApiVersionIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    void testOriginalApiVersionStillWorks() throws Exception {
        setUp();
        // 测试原有的ApiVersion功能仍然正常工作
        mockMvc.perform(get("/test-api")
                .header("version", "v1"))
                .andExpect(status().isOk())
                .andExpect(content().string("ApiVersion v1"));
    }

    @Test
    void testOriginalApiVersionHigherVersion() throws Exception {
        setUp();
        // 测试原有ApiVersion的版本匹配逻辑
        mockMvc.perform(get("/test-api")
                .header("version", "v2"))
                .andExpect(status().isOk())
                .andExpect(content().string("ApiVersion v2"));
    }

    @Test
    void testExtraApiVersionWithAreaMatch() throws Exception {
        setUp();
        // 测试ExtraApiVersion的区域匹配
        mockMvc.perform(get("/test-extra")
                .header("version", "v1")
                .header("area", "140000"))
                .andExpect(status().isOk())
                .andExpect(content().string("ExtraApiVersion v1 for area 14xxxx"));
    }

    @Test
    void testExtraApiVersionWithMultipleAreaMatch() throws Exception {
        setUp();
        // 测试ExtraApiVersion的多区域匹配
        mockMvc.perform(get("/test-extra")
                .header("version", "v2")
                .header("area", "320000"))
                .andExpect(status().isOk())
                .andExpect(content().string("ExtraApiVersion v2 for areas 14xxxx and 32xxxx"));
    }

    @Test
    void testExtraApiVersionWithoutAreaRestriction() throws Exception {
        setUp();
        // 测试ExtraApiVersion无区域限制的情况
        mockMvc.perform(get("/test-extra")
                .header("version", "v4"))
                .andExpect(status().isOk())
                .andExpect(content().string("ExtraApiVersion v4 without area restriction"));
    }

    @Test
    void testExtraApiVersionAreaMismatch() throws Exception {
        setUp();
        // 测试ExtraApiVersion区域不匹配的情况
        mockMvc.perform(get("/test-extra")
                .header("version", "v1")
                .header("area", "999999"))
                .andExpect(status().isOk())
                .andExpect(content().string("ExtraApiVersion v4 without area restriction")); // 应该匹配到无区域限制的版本
    }

    @Test
    void testVersionPriority() throws Exception {
        setUp();
        // 测试版本优先级 - 高版本优先
        mockMvc.perform(get("/test-extra")
                .header("version", "v3")
                .header("area", "330000"))
                .andExpect(status().isOk())
                .andExpect(content().string("ExtraApiVersion v3 for area 33xxxx"));
    }

    @Test
    void testMixedApiVersionAndExtraApiVersion() throws Exception {
        setUp();
        // 测试混合使用ApiVersion和ExtraApiVersion
        mockMvc.perform(get("/test-original")
                .header("version", "v1"))
                .andExpect(status().isOk())
                .andExpect(content().string("Original ApiVersion test"));

        mockMvc.perform(get("/test-mixed")
                .header("version", "v1")
                .header("area", "123456"))
                .andExpect(status().isOk())
                .andExpect(content().string("Mixed test with 6-digit area"));
    }

    @Test
    void testNoVersionHeader() throws Exception {
        setUp();
        // 测试没有版本头的情况
        mockMvc.perform(get("/test-api"))
                .andExpect(status().isOk())
                .andExpect(content().string("ApiVersion v1")); // 应该匹配到最低版本
    }

    @Test
    void testNoAreaHeaderWithAreaRestriction() throws Exception {
        setUp();
        // 测试没有区域头但有区域限制的情况
        mockMvc.perform(get("/test-extra")
                .header("version", "v1"))
                .andExpect(status().isOk())
                .andExpect(content().string("ExtraApiVersion v4 without area restriction")); // 应该匹配到无区域限制的版本
    }

    @Test
    void testInvalidVersionFormat() throws Exception {
        setUp();
        // 测试无效的版本格式
        mockMvc.perform(get("/test-api")
                .header("version", "invalid"))
                .andExpect(status().isOk())
                .andExpect(content().string("ApiVersion v1")); // 应该匹配到默认版本
    }
}