package com.apiversion.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletRequest;

import static org.junit.jupiter.api.Assertions.*;

class ExtraApiVersionConditionTest {

    private MockHttpServletRequest request;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
    }

    @Test
    void testVersionMatching_RequestVersionHigher() {
        // 测试请求版本高于注解版本的情况
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(1, new String[]{});
        request.addHeader("version", "v2");
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNotNull(result);
        assertEquals(1, result.getApiVersion());
    }

    @Test
    void testVersionMatching_RequestVersionEqual() {
        // 测试请求版本等于注解版本的情况
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(2, new String[]{});
        request.addHeader("version", "v2");
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNotNull(result);
        assertEquals(2, result.getApiVersion());
    }

    @Test
    void testVersionMatching_RequestVersionLower() {
        // 测试请求版本低于注解版本的情况
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(3, new String[]{});
        request.addHeader("version", "v2");
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNull(result);
    }

    @Test
    void testVersionMatching_NoVersionHeader() {
        // 测试没有版本头的情况
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(1, new String[]{});
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNotNull(result);
    }

    @Test
    void testAreaMatching_SinglePatternMatch() {
        // 测试单个区域模式匹配
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(1, new String[]{"^14\\d{4}$"});
        request.addHeader("version", "v1");
        request.addHeader("area", "140000");
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNotNull(result);
    }

    @Test
    void testAreaMatching_SinglePatternNoMatch() {
        // 测试单个区域模式不匹配
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(1, new String[]{"^14\\d{4}$"});
        request.addHeader("version", "v1");
        request.addHeader("area", "320000");
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNull(result);
    }

    @Test
    void testAreaMatching_MultiplePatterns() {
        // 测试多个区域模式匹配
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(1, 
            new String[]{"^14\\d{4}$", "^32\\d{4}$"});
        request.addHeader("version", "v1");
        request.addHeader("area", "320000");
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNotNull(result);
    }

    @Test
    void testAreaMatching_NoAreaHeader_WithAreaRestriction() {
        // 测试没有区域头但有区域限制的情况
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(1, new String[]{"^14\\d{4}$"});
        request.addHeader("version", "v1");
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNull(result);
    }

    @Test
    void testAreaMatching_NoAreaHeader_NoAreaRestriction() {
        // 测试没有区域头且没有区域限制的情况
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(1, new String[]{});
        request.addHeader("version", "v1");
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNotNull(result);
    }

    @Test
    void testAreaMatching_InvalidRegexPattern() {
        // 测试无效的正则表达式模式
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(1, new String[]{"[invalid"});
        request.addHeader("version", "v1");
        request.addHeader("area", "140000");
        
        ExtraApiVersionCondition result = condition.getMatchingCondition(request);
        assertNull(result); // 无效模式应该不匹配
    }

    @Test
    void testCombine() {
        // 测试条件合并
        ExtraApiVersionCondition condition1 = new ExtraApiVersionCondition(1, new String[]{"^14\\d{4}$"});
        ExtraApiVersionCondition condition2 = new ExtraApiVersionCondition(2, new String[]{"^32\\d{4}$"});
        
        ExtraApiVersionCondition combined = condition1.combine(condition2);
        
        assertEquals(2, combined.getApiVersion());
        assertArrayEquals(new String[]{"^32\\d{4}$"}, combined.getArea());
    }

    @Test
    void testCompareTo_DifferentVersions() {
        // 测试不同版本的比较
        ExtraApiVersionCondition condition1 = new ExtraApiVersionCondition(1, new String[]{});
        ExtraApiVersionCondition condition2 = new ExtraApiVersionCondition(2, new String[]{});
        
        int result = condition1.compareTo(condition2, request);
        assertTrue(result > 0); // 版本2应该优先于版本1
    }

    @Test
    void testCompareTo_SameVersionDifferentAreaCount() {
        // 测试相同版本但不同区域数量的比较
        ExtraApiVersionCondition condition1 = new ExtraApiVersionCondition(1, new String[]{});
        ExtraApiVersionCondition condition2 = new ExtraApiVersionCondition(1, new String[]{"^14\\d{4}$"});
        
        int result = condition1.compareTo(condition2, request);
        assertTrue(result > 0); // 有区域限制的应该优先
    }

    @Test
    void testCompareTo_SameConditions() {
        // 测试相同条件的比较
        ExtraApiVersionCondition condition1 = new ExtraApiVersionCondition(1, new String[]{"^14\\d{4}$"});
        ExtraApiVersionCondition condition2 = new ExtraApiVersionCondition(1, new String[]{"^32\\d{4}$"});
        
        int result = condition1.compareTo(condition2, request);
        assertEquals(0, result); // 应该相等
    }

    @Test
    void testGettersAndSetters() {
        // 测试getter方法
        String[] areas = {"^14\\d{4}$", "^32\\d{4}$"};
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(2, areas);
        
        assertEquals(2, condition.getApiVersion());
        assertArrayEquals(areas, condition.getArea());
    }

    @Test
    void testNullAreaArray() {
        // 测试null区域数组的处理
        ExtraApiVersionCondition condition = new ExtraApiVersionCondition(1, null);
        
        assertNotNull(condition.getArea());
        assertEquals(0, condition.getArea().length);
    }
}