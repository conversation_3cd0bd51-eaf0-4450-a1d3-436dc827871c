package com.apiversion.config;

import com.apiversion.annotation.ExtraApiVersion;
import com.shensu.common.base.annotation.ApiVersion;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.condition.RequestCondition;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

class ApiRequestMappingHandlerMappingTest {

    private ApiRequestMappingHandlerMapping handlerMapping;

    @BeforeEach
    void setUp() {
        handlerMapping = new ApiRequestMappingHandlerMapping();
    }

    @Test
    void testGetCustomTypeCondition_WithExtraApiVersion() throws Exception {
        // 测试类级别的ExtraApiVersion注解
        RequestCondition<?> condition = handlerMapping.getCustomTypeCondition(TestControllerWithExtraApiVersion.class);
        
        assertNotNull(condition);
        assertTrue(condition instanceof ExtraApiVersionCondition);
        ExtraApiVersionCondition extraCondition = (ExtraApiVersionCondition) condition;
        assertEquals(2, extraCondition.getApiVersion());
        assertArrayEquals(new String[]{"^14\\d{4}$"}, extraCondition.getArea());
    }

    @Test
    void testGetCustomTypeCondition_WithApiVersion() throws Exception {
        // 测试类级别的ApiVersion注解
        RequestCondition<?> condition = handlerMapping.getCustomTypeCondition(TestControllerWithApiVersion.class);
        
        assertNotNull(condition);
        assertTrue(condition instanceof ApiVersionCondition);
        ApiVersionCondition apiCondition = (ApiVersionCondition) condition;
        assertEquals(1, apiCondition.getApiVersion());
    }

    @Test
    void testGetCustomTypeCondition_NoAnnotation() throws Exception {
        // 测试没有注解的类
        RequestCondition<?> condition = handlerMapping.getCustomTypeCondition(TestControllerNoAnnotation.class);
        
        assertNull(condition);
    }

    @Test
    void testGetCustomMethodCondition_WithExtraApiVersion() throws Exception {
        // 测试方法级别的ExtraApiVersion注解
        Method method = TestControllerWithExtraApiVersion.class.getMethod("testMethod");
        RequestCondition<?> condition = handlerMapping.getCustomMethodCondition(method);
        
        assertNotNull(condition);
        assertTrue(condition instanceof ExtraApiVersionCondition);
        ExtraApiVersionCondition extraCondition = (ExtraApiVersionCondition) condition;
        assertEquals(3, extraCondition.getApiVersion());
        assertArrayEquals(new String[]{"^32\\d{4}$"}, extraCondition.getArea());
    }

    @Test
    void testGetCustomMethodCondition_WithApiVersion() throws Exception {
        // 测试方法级别的ApiVersion注解
        Method method = TestControllerWithApiVersion.class.getMethod("testMethod");
        RequestCondition<?> condition = handlerMapping.getCustomMethodCondition(method);
        
        assertNotNull(condition);
        assertTrue(condition instanceof ApiVersionCondition);
        ApiVersionCondition apiCondition = (ApiVersionCondition) condition;
        assertEquals(2, apiCondition.getApiVersion());
    }

    @Test
    void testGetCustomMethodCondition_NoAnnotation() throws Exception {
        // 测试没有注解的方法
        Method method = TestControllerNoAnnotation.class.getMethod("testMethod");
        RequestCondition<?> condition = handlerMapping.getCustomMethodCondition(method);
        
        assertNull(condition);
    }

    @Test
    void testPriorityExtraApiVersionOverApiVersion() throws Exception {
        // 测试ExtraApiVersion优先于ApiVersion
        Method method = TestControllerWithBothAnnotations.class.getMethod("testMethod");
        RequestCondition<?> condition = handlerMapping.getCustomMethodCondition(method);
        
        assertNotNull(condition);
        assertTrue(condition instanceof ExtraApiVersionCondition);
        ExtraApiVersionCondition extraCondition = (ExtraApiVersionCondition) condition;
        assertEquals(5, extraCondition.getApiVersion());
    }

    // 测试用的控制器类
    @ExtraApiVersion(value = 2, area = {"^14\\d{4}$"})
    @RestController
    static class TestControllerWithExtraApiVersion {
        
        @ExtraApiVersion(value = 3, area = {"^32\\d{4}$"})
        @GetMapping("/test")
        public String testMethod() {
            return "test";
        }
    }

    @ApiVersion(value = 1)
    @RestController
    static class TestControllerWithApiVersion {
        
        @ApiVersion(value = 2)
        @GetMapping("/test")
        public String testMethod() {
            return "test";
        }
    }

    @RestController
    static class TestControllerNoAnnotation {
        
        @GetMapping("/test")
        public String testMethod() {
            return "test";
        }
    }

    @RestController
    static class TestControllerWithBothAnnotations {
        
        @ExtraApiVersion(value = 5, area = {"^14\\d{4}$"})
        @ApiVersion(value = 3)
        @GetMapping("/test")
        public String testMethod() {
            return "test";
        }
    }
}