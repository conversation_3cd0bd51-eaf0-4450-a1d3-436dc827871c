# 设计文档

## 概述

ExtraApiVersion功能扩展现有的API版本管理系统，通过引入新的注解和条件处理机制，在保持与原有ApiVersion完全兼容的基础上，提供增强的区域匹配功能。设计采用并行扩展的方式，避免对现有功能的任何修改。

## 架构

### 核心设计原则

1. **向后兼容性**: 现有ApiVersion功能保持完全不变
2. **并行扩展**: ExtraApiVersion作为独立的扩展功能运行
3. **统一处理**: 通过扩展现有的RequestMappingHandlerMapping来统一处理两种注解
4. **条件分离**: 为ExtraApiVersion创建独立的条件处理类

### 架构组件关系

```
ApiRequestMappingHandlerMapping (扩展)
├── 处理 @ApiVersion (现有逻辑，保持不变)
│   └── 创建 ApiVersionCondition
└── 处理 @ExtraApiVersion (新增逻辑)
    └── 创建 ExtraApiVersionCondition
```

## 组件和接口

### 1. ExtraApiVersion 注解

**位置**: `com.apiversion.annotation.ExtraApiVersion`

**功能**: 定义扩展的API版本注解，包含version和area字段

**接口设计**:
```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExtraApiVersion {
    int value() default 1;
    String[] area() default {};
}
```

### 2. ExtraApiVersionCondition 条件类

**位置**: `com.apiversion.config.ExtraApiVersionCondition`

**功能**: 实现ExtraApiVersion的请求条件匹配逻辑

**核心方法**:
- `getMatchingCondition()`: 匹配请求条件
- `combine()`: 合并条件
- `compareTo()`: 条件比较

**匹配逻辑**:
1. 版本匹配: 请求版本 >= 注解版本
2. 区域匹配: 支持正则表达式模式匹配
3. 优先级: 版本号高的优先，区域匹配的优先

### 3. ApiRequestMappingHandlerMapping 扩展

**位置**: `com.apiversion.config.ApiRequestMappingHandlerMapping`

**功能**: 扩展现有的处理器映射，支持ExtraApiVersion

**扩展方法**:
- `getCustomTypeCondition()`: 处理类级别的注解
- `getCustomMethodCondition()`: 处理方法级别的注解
- `createExtraCondition()`: 创建ExtraApiVersion条件

### 4. 配置激活

**位置**: `com.apiversion.config.WebMvcRegistrationsConfig`

**功能**: 激活现有的配置类，启用扩展功能

## 数据模型

### 请求头参数

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| version | String | API版本号 | "v1", "v2" |
| area | String | 区域代码 | "140000", "320000" |

### 注解参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| value | int | 1 | API版本号 |
| area | String[] | {} | 区域匹配模式数组 |

## 错误处理

### 1. 版本不匹配
- **场景**: 请求版本低于注解版本
- **处理**: 返回null，不匹配该端点
- **行为**: Spring继续寻找其他匹配的端点

### 2. 区域不匹配
- **场景**: 请求区域不符合注解中的正则模式
- **处理**: 返回null，不匹配该端点
- **行为**: Spring继续寻找其他匹配的端点

### 3. 缺少请求头
- **场景**: 请求中缺少version或area头
- **处理**: 
  - 缺少version: 匹配所有版本
  - 缺少area: 只匹配没有area限制的端点

### 4. 正则表达式错误
- **场景**: area字段中的正则表达式格式错误
- **处理**: 记录警告日志，该模式不参与匹配
- **行为**: 继续处理其他有效模式

## 测试策略

### 1. 单元测试

**ExtraApiVersionCondition测试**:
- 版本匹配逻辑测试
- 区域正则匹配测试
- 条件合并测试
- 条件比较测试

**ApiRequestMappingHandlerMapping测试**:
- 注解识别测试
- 条件创建测试
- 类和方法级别注解处理测试

### 2. 集成测试

**端点路由测试**:
- 不同版本请求路由测试
- 不同区域请求路由测试
- 版本和区域组合匹配测试
- 优先级选择测试

**兼容性测试**:
- 现有ApiVersion功能不受影响测试
- 混合使用ApiVersion和ExtraApiVersion测试
- 配置加载测试

### 3. 边界测试

**异常情况测试**:
- 无效版本号处理
- 无效区域代码处理
- 缺少请求头处理
- 多个匹配端点的选择逻辑

## 实现细节

### 1. 条件匹配优先级

1. **精确匹配**: 版本和区域都匹配
2. **版本匹配**: 只有版本匹配，无区域限制
3. **默认匹配**: 无版本和区域限制

### 2. 正则表达式处理

- 使用Java Pattern类进行编译和匹配
- 支持标准正则表达式语法
- 模式编译错误时记录日志并跳过该模式

### 3. 性能考虑

- 正则表达式模式预编译和缓存
- 条件对象复用
- 快速失败的匹配逻辑

### 4. 日志记录

- 条件匹配过程的调试日志
- 正则表达式错误的警告日志
- 端点选择的信息日志