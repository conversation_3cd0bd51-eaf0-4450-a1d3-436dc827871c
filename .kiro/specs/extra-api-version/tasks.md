# 实施计划

- [x] 1. 创建 ExtraApiVersion 注解



  - 在 com.apiversion.annotation 包中创建 ExtraApiVersion 注解
  - 定义 value 和 area 字段，设置合适的默认值
  - 添加必要的元注解（@Target, @Retention, @Documented）
  - _需求: 1.1, 1.2_

- [x] 2. 实现 ExtraApiVersionCondition 条件类



  - 创建 ExtraApiVersionCondition 类，实现 RequestCondition 接口
  - 实现版本匹配逻辑（请求版本 >= 注解版本）
  - 实现区域正则表达式匹配逻辑
  - 实现 getMatchingCondition 方法处理请求匹配
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 3. 实现条件合并和比较逻辑


  - 在 ExtraApiVersionCondition 中实现 combine 方法
  - 实现 compareTo 方法，确保版本号高的优先
  - 添加适当的 getter 和 setter 方法
  - _需求: 3.1, 3.2, 3.4_

- [x] 4. 扩展 ApiRequestMappingHandlerMapping



  - 取消现有 ApiRequestMappingHandlerMapping 类的注释
  - 扩展 getCustomTypeCondition 方法支持 ExtraApiVersion 注解
  - 扩展 getCustomMethodCondition 方法支持 ExtraApiVersion 注解
  - 添加 createExtraCondition 方法创建 ExtraApiVersion 条件
  - 确保现有 ApiVersion 逻辑保持不变
  - _需求: 1.3, 1.4, 4.1, 4.2_

- [x] 5. 激活 WebMvcRegistrations 配置



  - 取消 WebMvcRegistrationsConfig 类的注释
  - 确保配置正确注册 ApiRequestMappingHandlerMapping
  - 验证 Spring 上下文能够正确加载配置
  - _需求: 4.3, 4.4_

- [x] 6. 创建 ExtraApiVersionCondition 单元测试



  - 测试版本匹配逻辑的各种场景
  - 测试区域正则表达式匹配功能
  - 测试条件合并和比较逻辑
  - 测试边界情况和异常处理
  - _需求: 2.1, 2.2, 2.3, 2.4, 3.1, 3.2, 3.4_

- [x] 7. 创建 ApiRequestMappingHandlerMapping 单元测试



  - 测试 ExtraApiVersion 注解的识别和处理
  - 测试条件创建逻辑
  - 测试类级别和方法级别注解的处理
  - 验证与现有 ApiVersion 功能的兼容性
  - _需求: 1.3, 1.4, 4.1, 4.2_

- [x] 8. 更新 UserController 添加 ExtraApiVersion 示例



  - 在现有 UserController 中添加使用 ExtraApiVersion 的方法
  - 展示不同的 area 配置示例
  - 确保与现有 ApiVersion 方法共存
  - 提供测试用的多种配置组合
  - _需求: 1.1, 1.2, 1.4, 2.1, 2.2_

- [x] 9. 创建集成测试验证端点路由



  - 创建集成测试类测试完整的请求路由流程
  - 测试不同 version 和 area 头的请求路由
  - 验证优先级选择逻辑
  - 测试 ExtraApiVersion 与 ApiVersion 的混合使用
  - _需求: 1.4, 3.1, 3.2, 4.1, 4.2_

- [x] 10. 添加错误处理和日志记录


  - 在 ExtraApiVersionCondition 中添加适当的日志记录
  - 处理正则表达式编译错误的情况
  - 添加调试信息帮助问题排查
  - 确保异常情况下的优雅降级
  - _需求: 2.3, 2.4_
